#!/usr/bin/env python3
"""
Zero-shot evaluation script for multiple models on dialogsum and qed datasets.
Evaluates: meta-llama/Llama-3.2-1B, meta-llama/Llama-3.2-3B, pythia410, opt350, bloomz 560
Datasets: dialogsum (rougeLSUM), qed (F1)
Limits: 500 samples from test set (dialogsum) and validation set (qed)
Logs: CSV file and wandb
"""

import os
import sys
import json
import csv
import logging
import argparse
import subprocess
from datetime import datetime
from pathlib import Path
import pandas as pd
import random
import numpy as np

# Add the llm_distillation directory to path
sys.path.append(f"{os.getcwd()}/llm_distillation")

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model configurations
MODEL_CONFIGS = {
    "meta-llama/Llama-3.2-1B": {
        "model_id": "meta-llama/Llama-3.2-1B",
        "model_tokenizer": None,  # Use same as model_id
        "seq2seq": False
    },
    "meta-llama/Llama-3.2-3B": {
        "model_id": "meta-llama/Llama-3.2-3B", 
        "model_tokenizer": None,
        "seq2seq": False
    },
    "pythia410": {
        "model_id": "EleutherAI/pythia-410m",
        "model_tokenizer": None,
        "seq2seq": False
    },
    "opt350": {
        "model_id": "facebook/opt-350m",
        "model_tokenizer": None,
        "seq2seq": False
    },
    "bloomz560": {
        "model_id": "bigscience/bloomz-560m",
        "model_tokenizer": None,
        "seq2seq": False
    }
}

# Dataset configurations
DATASET_CONFIGS = {
    "dialogsum": {
        "dataset_id": "knkarthick/dialogsum",
        "split_name": "test",
        "task": "summary_dialogue",
        "mapping": f"{os.getcwd()}/llm_distillation/benchmark/mapping/dialogsum.json",
        "primary_metric": "rougeLsum",
        "additional_metrics": ["rouge1", "rouge2", "rougeL"],
        "max_samples": 500,
        "from_disk": False
    },
    "qed": {
        "dataset_id": f"{os.getcwd()}/qed/qed.py",  # Local Python script
        "split_name": "validation",
        "task": "qa",
        "mapping": f"{os.getcwd()}/llm_distillation/benchmark/mapping/qed.json",
        "primary_metric": "f1",
        "additional_metrics": ["precision", "recall", "em"],
        "max_samples": 500,
        "from_disk": True  # Load from local script
    },
    "pubmedqa": {
        "dataset_id": "MothMalone/SLMS-KD-Benchmarks",
        "dataset_name": "pubmedqa",
        "split_name": "test",
        "task": "qa_medical",
        "mapping": f"{os.getcwd()}/llm_distillation/benchmark/mapping/pubmedqa.json",
        "primary_metric": "f1_bert",
        "additional_metrics": ["precision_bert", "recall_bert", "f1", "em"],
        "max_samples": 500,
        "from_disk": False
    }
}

def set_seed(seed=42):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    logger.info(f"Random seed set to {seed} for reproducibility")

def setup_wandb(api_key, project_name="zero-shot-evaluation"):
    """Initialize wandb with the provided API key."""
    try:
        import wandb

        # Set ALL possible wandb and system directories to avoid permission issues
        base_dir = "/storage/nammt/KD-SLM/Multi-Level-OT"

        # Override HOME directory completely
        os.environ["HOME"] = "/storage/nammt"
        os.environ["USERPROFILE"] = "/storage/nammt"  # Windows equivalent

        # Set all wandb-specific environment variables
        os.environ["WANDB_API_KEY"] = api_key
        os.environ["WANDB_DIR"] = base_dir
        os.environ["WANDB_CACHE_DIR"] = f"{base_dir}/.wandb_cache"
        os.environ["WANDB_CONFIG_DIR"] = f"{base_dir}/.wandb_config"
        os.environ["WANDB_DATA_DIR"] = f"{base_dir}/.wandb_data"
        os.environ["WANDB_ARTIFACT_DIR"] = f"{base_dir}/.wandb_artifacts"
        os.environ["WANDB_BASE_URL"] = "https://api.wandb.ai"
        os.environ["WANDB_CONSOLE"] = "off"  # Disable console logging that might cause issues
        os.environ["WANDB_SILENT"] = "false"  # Keep logging but control it

        # Create all wandb directories if they don't exist
        wandb_dirs = [
            f"{base_dir}/.wandb_cache",
            f"{base_dir}/.wandb_config",
            f"{base_dir}/.wandb_data",
            f"{base_dir}/.wandb_artifacts",
            f"/storage/nammt/.config",  # Create config dir in new HOME
            f"/storage/nammt/.cache"    # Create cache dir in new HOME
        ]

        for dir_path in wandb_dirs:
            os.makedirs(dir_path, exist_ok=True)

        # Force wandb to reinitialize with new settings
        if hasattr(wandb, '_initialized'):
            wandb._initialized = False

        wandb.login(key=api_key, relogin=True)
        logger.info("Wandb initialized successfully with custom directories")
        return wandb
    except ImportError:
        logger.error("wandb not installed. Install with: pip install wandb")
        return None
    except Exception as e:
        logger.error(f"Failed to initialize wandb: {e}")
        return None

def create_output_directories():
    """Create necessary output directories."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_dir = f"evaluation_results_{timestamp}"
    os.makedirs(base_dir, exist_ok=True)
    os.makedirs(f"{base_dir}/individual_results", exist_ok=True)
    return base_dir

def run_single_evaluation(model_name, model_config, dataset_name, dataset_config, output_dir, seed=42, wandb_api_key=None):
    """Run evaluation for a single model-dataset combination."""
    logger.info(f"Starting evaluation: {model_name} on {dataset_name}")

    # Determine which benchmark script to use
    if dataset_name == "dialogsum":
        script_path = f"{os.getcwd()}/llm_distillation/benchmark/benchmarkdialogsum.py"
    elif dataset_name == "qed":
        script_path = f"{os.getcwd()}/llm_distillation/benchmark/benchmarkqed.py"
    elif dataset_name == "pubmedqa":
        script_path = f"{os.getcwd()}/llm_distillation/benchmark/benchmarkpubmedqa.py"
    else:
        raise ValueError(f"Unknown dataset: {dataset_name}")

    # Create individual output directory
    individual_output = f"{output_dir}/individual_results/{model_name.replace('/', '_')}_{dataset_name}"
    os.makedirs(individual_output, exist_ok=True)

    # Build command
    cmd = [
        "python", script_path,
        "--model_id", model_config["model_id"],
        "--dataset_id", dataset_config["dataset_id"],
        "--split_name", dataset_config["split_name"],
        "--task", dataset_config["task"],
        "--mapping", dataset_config["mapping"],
        "--number_few_shot", "0",  # Zero-shot
        "--batch_size", "1",  # Use batch_size=1 for all datasets to avoid collation issues
        "--num_workers", "0",  # Use num_workers=0 for all datasets to avoid multiprocessing issues
        "--bfloat",
        "--output_path", individual_output,
        "--save_predictions",
        "--max_samples", str(dataset_config["max_samples"]),
        "--seed", str(seed)  # Add seed for reproducibility
    ]

    # Add dataset_name for PubMedQA (required for multi-dataset repositories)
    if dataset_name == "pubmedqa":
        cmd.extend(["--dataset_name", dataset_config["dataset_name"]])
        cmd.append("--bert_score")  # Always use bert_score for PubMedQA

    # Add wandb_api_key if provided
    if wandb_api_key:
        cmd.extend(["--wandb_api_key", wandb_api_key])

    # Add from_disk flag if needed (for QED dataset)
    if dataset_config.get("from_disk", False):
        cmd.append("--from_disk")
    
    # Add model tokenizer if specified
    if model_config["model_tokenizer"]:
        cmd.extend(["--model_tokenizer", model_config["model_tokenizer"]])
    
    # Add seq2seq flag if needed
    if model_config["seq2seq"]:
        cmd.append("--seq2seq")
    
    try:
        # Run the evaluation
        logger.info(f"Running command: {' '.join(cmd)}")
        logger.info("=" * 80)
        logger.info("STARTING INDIVIDUAL BENCHMARK - WANDB LOGS SHOULD APPEAR BELOW")
        logger.info("=" * 80)
        result = subprocess.run(cmd, capture_output=False, text=True, timeout=3600)  # 1 hour timeout, don't capture output
        
        if result.returncode != 0:
            logger.error(f"Evaluation failed for {model_name} on {dataset_name}")
            logger.error(f"Return code: {result.returncode}")
            return None
        
        # Load results
        results_file = f"{individual_output}/0shots.json"
        if os.path.exists(results_file):
            with open(results_file, 'r') as f:
                results = json.load(f)
            logger.info(f"Evaluation completed: {model_name} on {dataset_name}")
            return results
        else:
            logger.error(f"Results file not found: {results_file}")
            return None
            
    except subprocess.TimeoutExpired:
        logger.error(f"Evaluation timed out for {model_name} on {dataset_name}")
        return None
    except Exception as e:
        logger.error(f"Error during evaluation: {e}")
        return None

def save_results_to_csv(all_results, output_dir):
    """Save all results to a CSV file."""
    csv_file = f"{output_dir}/evaluation_results.csv"

    # Prepare data for CSV
    csv_data = []
    for result in all_results:
        row = {
            'model': result.get('model', 'unknown'),
            'dataset': result.get('dataset', 'unknown'),
            'primary_metric': result.get('primary_metric', 'unknown'),
            'primary_score': result.get('primary_score', 0),
            'samples_number': result.get('samples_number', 0),
            'timestamp': result.get('timestamp', datetime.now().isoformat())
        }

        # Add specific metrics we care about
        metrics_to_include = ['f1', 'em', 'squad', 'precision', 'recall',
                             'rougeL', 'rougeLsum', 'rouge1', 'rouge2',
                             'f1_bert', 'precision_bert', 'recall_bert']

        for metric in metrics_to_include:
            if metric in result:
                row[metric] = result[metric]

        csv_data.append(row)

    # Save to CSV
    df = pd.DataFrame(csv_data)
    df.to_csv(csv_file, index=False)
    logger.info(f"Results saved to CSV: {csv_file}")

    # Also save a summary CSV with just key metrics
    summary_data = []
    for result in all_results:
        summary_row = {
            'model': result.get('model', 'unknown'),
            'dataset': result.get('dataset', 'unknown'),
            'primary_metric': result.get('primary_metric', 'unknown'),
            'score': result.get('primary_score', 0),
            'samples': result.get('samples_number', 0)
        }
        summary_data.append(summary_row)

    summary_df = pd.DataFrame(summary_data)
    summary_file = f"{output_dir}/evaluation_summary.csv"
    summary_df.to_csv(summary_file, index=False)
    logger.info(f"Summary saved to CSV: {summary_file}")

    return csv_file

def log_to_wandb(wandb, all_results, project_name="zero-shot-evaluation"):
    """Log results to wandb."""
    if not wandb:
        logger.warning("Wandb not available, skipping wandb logging")
        return

    try:
        # Initialize a new run
        run = wandb.init(
            project=project_name,
            name=f"zero_shot_eval_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            config={
                "evaluation_type": "zero_shot",
                "datasets": list(set([r.get('dataset', 'unknown') for r in all_results])),
                "models": list(set([r.get('model', 'unknown') for r in all_results])),
                "total_evaluations": len(all_results)
            }
        )

        # Log metrics for each model-dataset combination
        metrics_to_log = {}
        for result in all_results:
            model_name = result.get('model', 'unknown').replace('/', '_').replace('-', '_')
            dataset_name = result.get('dataset', 'unknown')

            # Primary metric
            primary_metric = result.get('primary_metric', 'score')
            primary_score = result.get('primary_score', 0)
            metrics_to_log[f"{dataset_name}_{model_name}_{primary_metric}"] = primary_score
            metrics_to_log[f"{dataset_name}_{model_name}_samples"] = result.get('samples_number', 0)

            # Additional metrics based on dataset
            if dataset_name == "dialogsum":
                additional_metrics = ["rouge1", "rouge2", "rougeL"]
            elif dataset_name == "qed":
                additional_metrics = ["precision", "recall", "em"]
            elif dataset_name == "pubmedqa":
                additional_metrics = ["precision_bert", "recall_bert", "f1", "em"]
            else:
                additional_metrics = ['f1', 'em', 'squad', 'precision', 'recall', 'rougeL', 'rougeLsum', 'rouge1', 'rouge2']

            for metric in additional_metrics:
                if metric in result and isinstance(result[metric], (int, float)):
                    metrics_to_log[f"{dataset_name}_{model_name}_{metric}"] = result[metric]

        # Log all metrics at once
        wandb.log(metrics_to_log)

        # Create summary table
        summary_data = []
        for result in all_results:
            summary_data.append([
                result.get('model', 'unknown'),
                result.get('dataset', 'unknown'),
                result.get('primary_metric', 'unknown'),
                result.get('primary_score', 0),
                result.get('samples_number', 0)
            ])

        table = wandb.Table(
            columns=["Model", "Dataset", "Primary Metric", "Score", "Samples"],
            data=summary_data
        )
        wandb.log({"evaluation_summary": table})

        # Log individual result details
        for i, result in enumerate(all_results):
            wandb.log({f"result_{i}": result})

        wandb.finish()
        logger.info("Results logged to wandb successfully")

    except Exception as e:
        logger.error(f"Failed to log to wandb: {e}")
        logger.error(f"Wandb error details: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="Zero-shot evaluation of multiple models on dialogsum and qed datasets")
    parser.add_argument("--wandb_api_key", type=str, default=None,
                       help="Wandb API key for logging")
    parser.add_argument("--models", nargs="+", default=list(MODEL_CONFIGS.keys()),
                       help="Models to evaluate")
    parser.add_argument("--datasets", nargs="+", default=list(DATASET_CONFIGS.keys()),
                       help="Datasets to evaluate on")
    parser.add_argument("--project_name", type=str, default="zero-shot-evaluation",
                       help="Wandb project name")
    parser.add_argument("--dry_run", action="store_true",
                       help="Print what would be evaluated without running")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed for reproducibility")

    args = parser.parse_args()

    # Set seed for reproducibility
    set_seed(args.seed)

    # Validate inputs
    valid_models = [m for m in args.models if m in MODEL_CONFIGS]
    valid_datasets = [d for d in args.datasets if d in DATASET_CONFIGS]

    if not valid_models:
        logger.error(f"No valid models specified. Available: {list(MODEL_CONFIGS.keys())}")
        return

    if not valid_datasets:
        logger.error(f"No valid datasets specified. Available: {list(DATASET_CONFIGS.keys())}")
        return

    # Print evaluation plan
    logger.info("="*60)
    logger.info("ZERO-SHOT EVALUATION PLAN")
    logger.info("="*60)
    logger.info(f"Models to evaluate: {valid_models}")
    logger.info(f"Datasets to evaluate: {valid_datasets}")
    logger.info(f"Total evaluations: {len(valid_models) * len(valid_datasets)}")

    for model in valid_models:
        for dataset in valid_datasets:
            metric = DATASET_CONFIGS[dataset]['primary_metric']
            additional = ", ".join(DATASET_CONFIGS[dataset]['additional_metrics'])
            samples = DATASET_CONFIGS[dataset]['max_samples']
            logger.info(f"  - {model} on {dataset} ({metric} + {additional}, {samples} samples)")

    if args.dry_run:
        logger.info("Dry run completed. Use --no-dry-run to execute.")
        return

    # Setup
    output_dir = create_output_directories()
    wandb = setup_wandb(args.wandb_api_key, args.project_name)

    logger.info(f"\nStarting evaluations...")
    logger.info(f"Output directory: {output_dir}")

    # Run evaluations
    all_results = []
    total_evaluations = len(valid_models) * len(valid_datasets)
    current_evaluation = 0

    for model_name in valid_models:
        for dataset_name in valid_datasets:
            current_evaluation += 1
            logger.info(f"\n[{current_evaluation}/{total_evaluations}] Evaluating {model_name} on {dataset_name}")

            model_config = MODEL_CONFIGS[model_name]
            dataset_config = DATASET_CONFIGS[dataset_name]

            # Run evaluation
            result = run_single_evaluation(model_name, model_config, dataset_name, dataset_config, output_dir, args.seed, args.wandb_api_key)

            if result:
                # Add metadata
                result['primary_metric'] = dataset_config['primary_metric']
                result['primary_score'] = result.get(dataset_config['primary_metric'], 0)
                result['timestamp'] = datetime.now().isoformat()
                all_results.append(result)
                logger.info(f"✅ Completed: {result['primary_metric']} = {result['primary_score']:.2f}")
            else:
                logger.error(f"❌ Failed: {model_name} on {dataset_name}")

    # Save and log results
    if all_results:
        csv_file = save_results_to_csv(all_results, output_dir)
        log_to_wandb(wandb, all_results, args.project_name)

        # Print summary
        logger.info("\n" + "="*60)
        logger.info("EVALUATION SUMMARY")
        logger.info("="*60)
        for result in all_results:
            score = result.get('primary_score', 0)
            metric = result.get('primary_metric', 'unknown')
            logger.info(f"{result.get('model', 'unknown')} on {result.get('dataset', 'unknown')}: {metric} = {score:.2f}")

        logger.info(f"\n📊 Results saved to:")
        logger.info(f"  - CSV: {csv_file}")
        logger.info(f"  - Individual results: {output_dir}/individual_results/")
        if wandb:
            logger.info(f"  - Wandb: https://wandb.ai/{args.project_name}")

        logger.info(f"\n🎉 Evaluation completed successfully! ({len(all_results)}/{total_evaluations} successful)")
    else:
        logger.error("❌ No successful evaluations completed")

if __name__ == "__main__":
    main()
