import re
import string
import secrets
import evaluate
from collections import Counter
import torch
import sys
import os

# Add path to custom bertscore implementation
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'bertscore'))

def _normalize(s):
  def remove_articles(text):
    regex = re.compile(r'\b(a|an|the)\b', re.UNICODE)
    return re.sub(regex, ' ', text)
  def white_space_fix(text):
    return ' '.join(text.split())
  def remove_punc(text):
    exclude = set(string.punctuation)
    return ''.join(ch for ch in text if ch not in exclude)
  def lower(text):
    return text.lower()
  return white_space_fix(remove_articles(remove_punc(lower(s))))

def _f1_score_sentence(prediction, answer):
    prediction_tokens = prediction.split()
    answer_tokens = answer.split()
    
    common = Counter(prediction_tokens) & Counter(answer_tokens)
    num_common = sum(common.values())
    
    if num_common == 0:
        return 0, 0, 0
    
    precision = num_common / len(prediction_tokens)
    recall = num_common / len(answer_tokens)
    f1 = 2 * precision * recall / (precision + recall)
    return f1, precision, recall

def f1_score(predictions, answers):
    f1_scores, precision_scores, recall_scores = [], [], []

    for prediction, answer_list in zip(predictions, answers):
        prediction = _normalize(prediction)
        max_f1, max_precision, max_recall = 0, 0, 0

        if not answer_list:
          if prediction == "" or 'no response' in prediction:
            max_f1 = max_precision = max_recall = 1
          else:
            max_f1 = max_precision = max_recall = 0
        else:
          if isinstance(answer_list, str): answer_list = [answer_list]
          for answer in answer_list:
            answer = _normalize(answer)
            f1, precision, recall = _f1_score_sentence(prediction, answer)
            max_f1, max_precision, max_recall = max(f1, max_f1), max(precision, max_precision), max(recall, max_recall)

        f1_scores.append(max_f1)
        precision_scores.append(max_precision)
        recall_scores.append(max_recall)

    average_f1 = sum(f1_scores) / len(f1_scores)
    average_precision = sum(precision_scores) / len(precision_scores)
    average_recall = sum(recall_scores) / len(recall_scores)

    return {'f1': average_f1, 'precision': average_precision, 'recall': average_recall}

def exact_match(predictions, answers):
    exact_match_scores = []
    for prediction, answer_list in zip(predictions, answers):
        prediction = _normalize(prediction)
        if isinstance(answer_list, str): answer_list = [answer_list]
        answer_list = [_normalize(item) for item in answer_list]
        if not answer_list and prediction == "" or "no response" in prediction: exact_match_scores.append(1)
        if prediction in answer_list: exact_match_scores.append(1)
        else: exact_match_scores.append(0)
    return sum(exact_match_scores)/len(exact_match_scores)

def rouge(predictions, answers):
    rouge_metric = evaluate.load('rouge', experiment_id=f"{secrets.randbelow(10000)}")
    if True:
      return rouge_metric.compute(predictions=predictions, references=answers)
    else:
      max_score = 0
      average = 0
       # Iterate over predictions and corresponding sets of reference answers
      for prediction, references in zip(predictions, answers):
        scores = []
        for reference in references:
            # Compute the ROUGE score for each reference
            score = rouge_metric.compute(predictions=[prediction], references=[reference])
            scores.append(score)
        average += (scores[0]["rougeLsum"]+scores[1]["rougeLsum"]+scores[2]["rougeLsum"])/3
        max_score +=  max(score["rougeLsum"] for score in scores)
        # print(rouge_metric.compute(predictions=[prediction], references=[references]))
        # print(max_score)
        # input()
        
      return {"rougeLsum":average/len(predictions)}


def bert_score(predictions, answers, device='cpu'):
    """
    Compute BERTScore using the same configuration as the custom BERTScore implementation.
    Uses the exact same default parameters to ensure consistent results.
    """
    try:
        # Try to load custom BERTScore implementation
        from bertscore import BERTScore
        bertscore_metric = BERTScore()
    except ImportError:
        # Fallback to standard evaluate implementation
        bertscore_metric = evaluate.load("bertscore", experiment_id=f"{secrets.randbelow(10000)}")

    # Use the exact same default parameters as the custom BERTScore implementation
    # These match the defaults in the _compute method of your custom BERTScore class
    default_params = {
        'lang': 'en',  # Default language
        'model_type': None,  # Will be auto-selected based on lang
        'num_layers': None,  # Will be auto-selected based on model_type
        'verbose': False,
        'idf': False,
        'device': device,
        'batch_size': 64,
        'nthreads': 4,
        'all_layers': False,
        'rescale_with_baseline': False,  # This is the key default that was True in your old implementation
        'baseline_path': None,
        'use_fast_tokenizer': False
    }

    # Handle different answer formats - simplified to match your custom implementation behavior
    if len(answers) == 0:
        return {'f1': [], 'precision': [], 'recall': []}

    # For simple string lists (most common case for PubMedQA)
    if isinstance(answers[0], str):
        results = bertscore_metric.compute(
            predictions=predictions,
            references=answers,
            **default_params
        )
        return {
            'f1': results['f1'],
            'precision': results['precision'],
            'recall': results['recall']
        }

    # For other formats, compute individually to handle max scoring
    else:
        f1_scores, precision_scores, recall_scores = [], [], []

        for i, (pred, answer_item) in enumerate(zip(predictions, answers)):
            max_f1, max_precision, max_recall = 0, 0, 0

            # Extract answer strings based on format
            if isinstance(answer_item, dict):
                answer_strings = [str(v) for v in answer_item.values() if v is not None and str(v).strip()]
            elif isinstance(answer_item, list):
                answer_strings = [str(a) for a in answer_item if a is not None and str(a).strip()]
            else:
                answer_strings = [str(answer_item)]

            if not answer_strings:
                f1_scores.append(0)
                precision_scores.append(0)
                recall_scores.append(0)
                continue

            # Compute scores against each answer and take maximum
            for answer in answer_strings:
                tmp_results = bertscore_metric.compute(
                    predictions=[pred],
                    references=[answer],
                    **default_params
                )

                max_f1 = max(max_f1, tmp_results['f1'][0])
                max_precision = max(max_precision, tmp_results['precision'][0])
                max_recall = max(max_recall, tmp_results['recall'][0])

            f1_scores.append(max_f1)
            precision_scores.append(max_precision)
            recall_scores.append(max_recall)

        return {'f1': f1_scores, 'precision': precision_scores, 'recall': recall_scores}



